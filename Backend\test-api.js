import axios from 'axios';

const BASE_URL = 'http://localhost:5000/api';

// Test data
const testData = {
  superAdminRW: {
    username: 'superadmin_rw',
    password: 'SuperAdmin123!'
  },
  superAdminRO1: {
    username: 'superadmin_ro1',
    password: 'SuperAdmin123!'
  },
  superAdminRO2: {
    username: 'superadmin_ro2',
    password: 'SuperAdmin123!'
  },
  testDepartment: {
    name: `Engineering_${Date.now()}`,
    description: 'Software Engineering Department'
  },
  testAdmin: {
    name: '<PERSON>',
    email: `john.admin.${Date.now()}@company.com`,
    username: `johnadmin_${Date.now()}`,
    password: 'Admin123!',
    confirmPassword: 'Admin123!'
  },
  testEmployee: {
    name: '<PERSON> Employee',
    email: `jane.employee.${Date.now()}@company.com`,
    contactNumber: '+1234567890',
    address: '123 Main St, City, State',
    username: `janeemployee_${Date.now()}`,
    password: 'Employee123!',
    confirmPassword: 'Employee123!'
  }
};

let tokens = {};
let createdData = {};

async function testAPI() {
  console.log('🚀 Starting API Tests...\n');

  try {
    // Test 1: SuperAdmin Read-Write Login
    console.log('1️⃣ Testing SuperAdmin Read-Write Login...');
    const superAdminRWLogin = await axios.post(`${BASE_URL}/auth/superadmin/login`, testData.superAdminRW);
    tokens.superAdminRW = superAdminRWLogin.data.token;
    console.log('✅ SuperAdmin Read-Write login successful');
    console.log(`   User: ${superAdminRWLogin.data.user.name} (${superAdminRWLogin.data.user.role})`);
    console.log(`   Token: ${tokens.superAdminRW.substring(0, 20)}...\n`);

    // Test 2: SuperAdmin Read-Only Login
    console.log('2️⃣ Testing SuperAdmin Read-Only Login...');
    const superAdminRO1Login = await axios.post(`${BASE_URL}/auth/superadmin/login`, testData.superAdminRO1);
    tokens.superAdminRO1 = superAdminRO1Login.data.token;
    console.log('✅ SuperAdmin Read-Only 1 login successful');
    console.log(`   User: ${superAdminRO1Login.data.user.name} (${superAdminRO1Login.data.user.role})`);
    console.log(`   Token: ${tokens.superAdminRO1.substring(0, 20)}...\n`);

    // Test 3: Create Department (Read-Write SuperAdmin)
    console.log('3️⃣ Testing Department Creation (Read-Write SuperAdmin)...');
    const departmentResponse = await axios.post(
      `${BASE_URL}/department/create`,
      testData.testDepartment,
      {
        headers: { Authorization: `Bearer ${tokens.superAdminRW}` }
      }
    );
    createdData.department = departmentResponse.data.department;
    console.log('✅ Department created successfully');
    console.log(`   Department: ${createdData.department.name} (ID: ${createdData.department._id})\n`);

    // Test 4: Try to Create Department with Read-Only SuperAdmin (Should Fail)
    console.log('4️⃣ Testing Department Creation with Read-Only SuperAdmin (Should Fail)...');
    try {
      await axios.post(
        `${BASE_URL}/department/create`,
        { name: 'Marketing', description: 'Marketing Department' },
        {
          headers: { Authorization: `Bearer ${tokens.superAdminRO1}` }
        }
      );
      console.log('❌ ERROR: Read-Only SuperAdmin should not be able to create departments!');
    } catch (error) {
      console.log('✅ Correctly blocked Read-Only SuperAdmin from creating department');
      console.log(`   Error: ${error.response.data.message}\n`);
    }

    // Test 5: Create Admin (Read-Write SuperAdmin)
    console.log('5️⃣ Testing Admin Creation...');
    const adminData = {
      ...testData.testAdmin,
      department: createdData.department._id
    };
    const adminResponse = await axios.post(
      `${BASE_URL}/admin/create`,
      adminData,
      {
        headers: { Authorization: `Bearer ${tokens.superAdminRW}` }
      }
    );
    createdData.admin = adminResponse.data.admin;
    console.log('✅ Admin created successfully');
    console.log(`   Admin: ${createdData.admin.name} (${createdData.admin.username})`);
    console.log(`   Department: ${createdData.admin.department.name}\n`);

    // Test 6: Admin Login
    console.log('6️⃣ Testing Admin Login...');
    const adminLogin = await axios.post(`${BASE_URL}/auth/admin/login`, {
      username: testData.testAdmin.username,
      password: testData.testAdmin.password
    });
    tokens.admin = adminLogin.data.token;
    console.log('✅ Admin login successful');
    console.log(`   User: ${adminLogin.data.user.name} (${adminLogin.data.user.role})`);
    console.log(`   Department: ${adminLogin.data.user.department.name}\n`);

    // Test 7: Employee Registration (Test Mode - No Email)
    console.log('7️⃣ Testing Employee Registration (Test Mode)...');
    const employeeData = {
      ...testData.testEmployee,
      department: createdData.department._id
    };
    try {
      const employeeRegResponse = await axios.post(`${BASE_URL}/test/create-employee`, employeeData);
      createdData.employee = employeeRegResponse.data.employee;
      console.log('✅ Test employee created successfully (email verification bypassed)');
      console.log(`   Employee ID: ${createdData.employee.id}`);
      console.log(`   Name: ${createdData.employee.name} (${createdData.employee.username})`);
      console.log(`   Email Verified: ${createdData.employee.isEmailVerified}\n`);
    } catch (error) {
      console.log('❌ Employee creation failed');
      console.log(`   Error: ${error.response?.data?.message || error.message}\n`);
      createdData.employee = null;
    }

    // Test 8: Employee Login (Should Work - Test Mode)
    if (createdData.employee) {
      console.log('8️⃣ Testing Employee Login...');
      try {
        const employeeLogin = await axios.post(`${BASE_URL}/auth/employee/login`, {
          username: testData.testEmployee.username,
          password: testData.testEmployee.password
        });
        tokens.employee = employeeLogin.data.token;
        console.log('✅ Employee login successful');
        console.log(`   User: ${employeeLogin.data.user.name} (${employeeLogin.data.user.role})`);
        console.log(`   Department: ${employeeLogin.data.user.department.name}\n`);
      } catch (error) {
        console.log('❌ Employee login failed');
        console.log(`   Error: ${error.response?.data?.message || error.message}\n`);
      }
    } else {
      console.log('8️⃣ Skipping employee login test (registration failed)\n');
    }

    // Test 9: Get All Departments
    console.log('9️⃣ Testing Get All Departments...');
    const departmentsResponse = await axios.get(`${BASE_URL}/department/all`);
    console.log('✅ Retrieved all departments');
    console.log(`   Total departments: ${departmentsResponse.data.departments.length}`);
    departmentsResponse.data.departments.forEach(dept => {
      console.log(`   - ${dept.name}: ${dept.description} (Active: ${dept.isActive})`);
    });
    console.log();

    // Test 10: Get All Admins (Any SuperAdmin)
    console.log('🔟 Testing Get All Admins with Read-Only SuperAdmin...');
    const adminsResponse = await axios.get(`${BASE_URL}/admin/all`, {
      headers: { Authorization: `Bearer ${tokens.superAdminRO1}` }
    });
    console.log('✅ Read-Only SuperAdmin can view admins');
    console.log(`   Total admins: ${adminsResponse.data.admins.length}`);
    adminsResponse.data.admins.forEach(admin => {
      console.log(`   - ${admin.name} (${admin.username}) - Department: ${admin.department.name}`);
    });
    console.log();

    console.log('🎉 All API tests completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('✅ SuperAdmin Read-Write login');
    console.log('✅ SuperAdmin Read-Only login');
    console.log('✅ Department creation (Read-Write only)');
    console.log('✅ Permission blocking (Read-Only cannot create)');
    console.log('✅ Admin creation and login');
    console.log('✅ Employee registration (OTP sent)');
    console.log('✅ Login blocking for unverified employees');
    console.log('✅ Data retrieval with proper permissions');

    console.log('\n📧 Note: Employee email verification requires manual OTP entry');
    console.log('Check your email service for the OTP to complete employee verification.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the tests
testAPI();
