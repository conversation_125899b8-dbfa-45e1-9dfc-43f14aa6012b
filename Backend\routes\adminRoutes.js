import express from "express"
import { createAdmin, getAllAdmins, updateAdminPassword, toggleAdminStatus } from "../controllers/adminController.js"
import { verifyToken, superAdminReadWrite, anySuperAdmin } from "../middleware/auth.js"

const router = express.Router()

router.post("/create", verifyToken, superAdminReadWrite, createAdmin)
router.get("/all", verifyToken, anySuperAdmin, getAllAdmins)
router.put("/:adminId/password", verifyToken, superAdminReadWrite, updateAdminPassword)
router.put("/:adminId/toggle-status", verifyToken, superAdminReadWrite, toggleAdminStatus)

export default router
