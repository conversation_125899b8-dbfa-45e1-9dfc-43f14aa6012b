import express from "express"
import {
  createTask,
  getAllTasks,
  getTaskById,
  updateTask,
  deleteTask,
  startTimeTracking,
  stopTimeTracking,
  addTimeEntry,
  getTaskTimeTracking,
} from "../controllers/taskController.js"
import { getTaskReports, getEmployeePerformanceReport } from "../controllers/reportController.js"
import { 
  verifyToken, 
  adminOrSuperAdmin, 
  employeeOnly, 
  anySuperAdmin,
  superAdminReadWrite 
} from "../middleware/auth.js"

const router = express.Router()

// Task CRUD operations
router.post("/create", verifyToken, adminOrSuperAdmin, createTask)
router.get("/all", verifyToken, getAllTasks)
router.get("/:taskId", verifyToken, getTaskById)
router.put("/:taskId", verifyToken, updateTask)
router.delete("/:taskId", verifyToken, adminOrSuperAdmin, deleteTask)

// Time tracking operations (Employee only)
router.post("/:taskId/start-tracking", verifyToken, employeeOnly, startTimeTracking)
router.post("/:taskId/stop-tracking", verifyToken, employeeOnly, stopTimeTracking)
router.post("/:taskId/add-time-entry", verifyToken, employeeOnly, addTimeEntry)
router.get("/:taskId/time-tracking", verifyToken, getTaskTimeTracking)

// Reporting endpoints
router.get("/reports/tasks", verifyToken, anySuperAdmin, getTaskReports)
router.get("/reports/employee-performance", verifyToken, getEmployeePerformanceReport)

export default router
