import axios from 'axios';

const BASE_URL = 'http://localhost:5000/api';

async function testEmailVerificationFlow() {
  console.log('📧 Testing Email Verification Flow with Frontend Integration...\n');

  try {
    // Get departments first
    console.log('1️⃣ Getting available departments...');
    const departmentsResponse = await axios.get(`${BASE_URL}/department/all`);
    const departments = departmentsResponse.data.departments;
    
    if (departments.length === 0) {
      console.log('❌ No departments found. Please create a department first.');
      return;
    }
    
    const department = departments[0];
    console.log(`✅ Using department: ${department.name} (ID: ${department._id})\n`);

    // Test 1: Employee Registration
    console.log('2️⃣ Testing Employee Registration...');
    const employeeData = {
      name: 'Test Employee Email Verification',
      email: `test.email.verification.${Date.now()}@company.com`,
      contactNumber: '+1234567890',
      address: '123 Test Email St',
      username: `testemail_${Date.now()}`,
      password: 'TestEmail123!',
      department: department._id
    };

    const registrationResponse = await axios.post(`${BASE_URL}/auth/employee/register`, employeeData);
    
    console.log('✅ Employee registration response:');
    console.log(`   Success: ${registrationResponse.data.success}`);
    console.log(`   Message: ${registrationResponse.data.message}`);
    console.log(`   Employee ID: ${registrationResponse.data.data.employeeId}`);
    console.log(`   Email: ${registrationResponse.data.data.email}`);
    console.log(`   Test Mode: ${registrationResponse.data.data.testMode}`);
    if (registrationResponse.data.data.testOtp) {
      console.log(`   Test OTP: ${registrationResponse.data.data.testOtp}`);
    }
    console.log();

    // Test 2: Try login before verification (should fail)
    console.log('3️⃣ Testing Login Before Email Verification (Should Fail)...');
    try {
      await axios.post(`${BASE_URL}/auth/employee/login`, {
        username: employeeData.username,
        password: employeeData.password
      });
      console.log('❌ ERROR: Employee should not be able to login before verification!');
    } catch (error) {
      console.log('✅ Correctly blocked unverified employee from logging in');
      console.log(`   Error: ${error.response.data.message}\n`);
    }

    // Test 3: Email verification with wrong OTP (should fail)
    console.log('4️⃣ Testing Email Verification with Wrong OTP (Should Fail)...');
    try {
      await axios.post(`${BASE_URL}/auth/employee/verify-email`, {
        employeeId: registrationResponse.data.data.employeeId,
        otp: '999999' // Wrong OTP
      });
      console.log('❌ ERROR: Wrong OTP should not work!');
    } catch (error) {
      console.log('✅ Correctly rejected wrong OTP');
      console.log(`   Error: ${error.response.data.message}\n`);
    }

    // Test 4: Email verification with correct OTP
    console.log('5️⃣ Testing Email Verification with Correct OTP...');
    const correctOtp = registrationResponse.data.data.testOtp || '123456';
    
    const verificationResponse = await axios.post(`${BASE_URL}/auth/employee/verify-email`, {
      employeeId: registrationResponse.data.data.employeeId,
      otp: correctOtp
    });

    console.log('✅ Email verification successful:');
    console.log(`   Success: ${verificationResponse.data.success}`);
    console.log(`   Message: ${verificationResponse.data.message}`);
    console.log(`   Employee: ${verificationResponse.data.data.name}`);
    console.log(`   Email: ${verificationResponse.data.data.email}\n`);

    // Test 5: Login after verification (should work)
    console.log('6️⃣ Testing Login After Email Verification (Should Work)...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/employee/login`, {
      username: employeeData.username,
      password: employeeData.password
    });

    console.log('✅ Employee login successful after verification:');
    console.log(`   Success: ${loginResponse.data.success}`);
    console.log(`   Employee: ${loginResponse.data.user.name}`);
    console.log(`   Role: ${loginResponse.data.user.role}`);
    console.log(`   Department: ${loginResponse.data.user.department.name}`);
    console.log(`   Token: ${loginResponse.data.token.substring(0, 20)}...\n`);

    // Test 6: Try to verify already verified email (should fail gracefully)
    console.log('7️⃣ Testing Double Verification (Should Fail Gracefully)...');
    try {
      await axios.post(`${BASE_URL}/auth/employee/verify-email`, {
        employeeId: registrationResponse.data.data.employeeId,
        otp: correctOtp
      });
      console.log('❌ ERROR: Should not allow double verification!');
    } catch (error) {
      console.log('✅ Correctly handled double verification attempt');
      console.log(`   Error: ${error.response.data.message}\n`);
    }

    console.log('🎉 All Email Verification Tests Completed Successfully!');
    console.log('\n📋 Test Summary:');
    console.log('✅ Employee registration with email verification setup');
    console.log('✅ Login blocking for unverified accounts');
    console.log('✅ Wrong OTP rejection');
    console.log('✅ Correct OTP acceptance and account activation');
    console.log('✅ Login success after verification');
    console.log('✅ Double verification prevention');

    console.log('\n🎯 Frontend Integration Ready:');
    console.log('• Employee registration form with email verification flow');
    console.log('• Email verification component with test OTP display');
    console.log('• Automatic redirect to login after successful verification');
    console.log('• Error handling for all verification scenarios');
    console.log('• Test mode support when email service is not configured');

    console.log('\n🔗 Frontend Test URLs:');
    console.log('• Employee Registration: http://localhost:5175/employee/register');
    console.log('• Employee Login: http://localhost:5175/employee/login');

    console.log('\n📝 How to Test in Frontend:');
    console.log('1. Go to http://localhost:5175/employee/register');
    console.log('2. Fill out the registration form');
    console.log('3. Submit the form');
    console.log('4. You will see the email verification screen');
    console.log('5. Use the displayed test OTP (123456 or the one shown)');
    console.log('6. Click "Verify Email"');
    console.log('7. You will be redirected to login');
    console.log('8. Login with your credentials');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the tests
testEmailVerificationFlow();
