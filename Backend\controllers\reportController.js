import Task from "../models/Task.js"
import Employee from "../models/Employee.js"
import Department from "../models/Department.js"
import mongoose from "mongoose"

// Get comprehensive task reports for superadmins
export const getTaskReports = async (req, res) => {
  try {
    const {
      departmentId,
      startDate,
      endDate,
      employeeId,
      status,
      reportType = "summary", // summary, detailed, time-tracking
    } = req.query

    // Build date filter
    const dateFilter = {}
    if (startDate) dateFilter.$gte = new Date(startDate)
    if (endDate) dateFilter.$lte = new Date(endDate)

    // Build main filter
    const filter = { isActive: true }
    if (Object.keys(dateFilter).length > 0) {
      filter.createdAt = dateFilter
    }

    // Role-based filtering
    if (req.user.role === "admin") {
      filter.department = req.user.department
    } else if (departmentId) {
      filter.department = departmentId
    }

    if (employeeId) filter.assignedTo = employeeId
    if (status) filter.status = status

    let report = {}

    if (reportType === "summary") {
      // Summary report with aggregated data
      const summaryData = await Task.aggregate([
        { $match: filter },
        {
          $group: {
            _id: null,
            totalTasks: { $sum: 1 },
            completedTasks: {
              $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
            },
            inProgressTasks: {
              $sum: { $cond: [{ $eq: ["$status", "in-progress"] }, 1, 0] },
            },
            pendingTasks: {
              $sum: { $cond: [{ $eq: ["$status", "pending"] }, 1, 0] },
            },
            cancelledTasks: {
              $sum: { $cond: [{ $eq: ["$status", "cancelled"] }, 1, 0] },
            },
            totalEstimatedHours: { $sum: "$estimatedHours" },
            totalActualHours: { $sum: "$actualHours" },
            averageCompletionTime: {
              $avg: {
                $cond: [
                  { $eq: ["$status", "completed"] },
                  {
                    $divide: [
                      { $subtract: ["$completedDate", "$startDate"] },
                      1000 * 60 * 60 * 24, // Convert to days
                    ],
                  },
                  null,
                ],
              },
            },
          },
        },
      ])

      // Department-wise breakdown
      const departmentBreakdown = await Task.aggregate([
        { $match: filter },
        {
          $group: {
            _id: "$department",
            totalTasks: { $sum: 1 },
            completedTasks: {
              $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
            },
            totalHours: { $sum: "$actualHours" },
          },
        },
        {
          $lookup: {
            from: "departments",
            localField: "_id",
            foreignField: "_id",
            as: "department",
          },
        },
        { $unwind: "$department" },
        {
          $project: {
            departmentName: "$department.name",
            totalTasks: 1,
            completedTasks: 1,
            totalHours: 1,
            completionRate: {
              $multiply: [{ $divide: ["$completedTasks", "$totalTasks"] }, 100],
            },
          },
        },
      ])

      report = {
        summary: summaryData[0] || {
          totalTasks: 0,
          completedTasks: 0,
          inProgressTasks: 0,
          pendingTasks: 0,
          cancelledTasks: 0,
          totalEstimatedHours: 0,
          totalActualHours: 0,
          averageCompletionTime: 0,
        },
        departmentBreakdown,
        completionRate:
          summaryData[0]?.totalTasks > 0
            ? Math.round((summaryData[0].completedTasks / summaryData[0].totalTasks) * 100)
            : 0,
        efficiencyRate:
          summaryData[0]?.totalEstimatedHours > 0
            ? Math.round((summaryData[0].totalEstimatedHours / summaryData[0].totalActualHours) * 100)
            : 0,
      }
    } else if (reportType === "detailed") {
      // Detailed report with individual task data
      const tasks = await Task.find(filter)
        .populate("assignedTo", "name email username")
        .populate("assignedBy", "name email username")
        .populate("department", "name description")
        .sort({ createdAt: -1 })
        .limit(1000) // Limit for performance

      report = {
        tasks,
        totalCount: await Task.countDocuments(filter),
      }
    } else if (reportType === "time-tracking") {
      // Time tracking focused report
      const timeTrackingData = await Task.aggregate([
        { $match: filter },
        { $unwind: { path: "$timeEntries", preserveNullAndEmptyArrays: true } },
        {
          $group: {
            _id: {
              taskId: "$_id",
              employeeId: "$assignedTo",
              departmentId: "$department",
            },
            taskTitle: { $first: "$title" },
            totalMinutes: { $sum: "$timeEntries.duration" },
            totalEntries: { $sum: 1 },
            estimatedHours: { $first: "$estimatedHours" },
            actualHours: { $first: "$actualHours" },
            status: { $first: "$status" },
          },
        },
        {
          $lookup: {
            from: "employees",
            localField: "_id.employeeId",
            foreignField: "_id",
            as: "employee",
          },
        },
        {
          $lookup: {
            from: "departments",
            localField: "_id.departmentId",
            foreignField: "_id",
            as: "department",
          },
        },
        { $unwind: "$employee" },
        { $unwind: "$department" },
        {
          $project: {
            taskTitle: 1,
            employeeName: "$employee.name",
            departmentName: "$department.name",
            totalHours: { $divide: ["$totalMinutes", 60] },
            totalEntries: 1,
            estimatedHours: 1,
            actualHours: 1,
            status: 1,
            efficiency: {
              $cond: [
                { $gt: ["$estimatedHours", 0] },
                { $multiply: [{ $divide: ["$estimatedHours", "$actualHours"] }, 100] },
                0,
              ],
            },
          },
        },
        { $sort: { totalHours: -1 } },
      ])

      report = {
        timeTrackingData,
        totalTrackedHours: timeTrackingData.reduce((sum, item) => sum + item.totalHours, 0),
      }
    }

    res.json({
      success: true,
      reportType,
      filters: {
        departmentId,
        startDate,
        endDate,
        employeeId,
        status,
      },
      generatedAt: new Date(),
      report,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Get employee performance report
export const getEmployeePerformanceReport = async (req, res) => {
  try {
    const { employeeId, startDate, endDate } = req.query

    // Build date filter
    const dateFilter = {}
    if (startDate) dateFilter.$gte = new Date(startDate)
    if (endDate) dateFilter.$lte = new Date(endDate)

    const filter = { isActive: true }
    if (Object.keys(dateFilter).length > 0) {
      filter.createdAt = dateFilter
    }

    // Role-based filtering
    if (req.user.role === "employee") {
      filter.assignedTo = req.user.id
    } else if (employeeId) {
      filter.assignedTo = employeeId
    } else {
      return res.status(400).json({
        success: false,
        message: "Employee ID is required",
      })
    }

    // If admin, ensure employee is in their department
    if (req.user.role === "admin") {
      const employee = await Employee.findById(filter.assignedTo)
      if (!employee || employee.department.toString() !== req.user.department.toString()) {
        return res.status(403).json({
          success: false,
          message: "Access denied",
        })
      }
    }

    const performanceData = await Task.aggregate([
      { $match: filter },
      {
        $group: {
          _id: null,
          totalTasks: { $sum: 1 },
          completedTasks: {
            $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
          },
          onTimeTasks: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ["$status", "completed"] },
                    { $lte: ["$completedDate", "$dueDate"] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          totalEstimatedHours: { $sum: "$estimatedHours" },
          totalActualHours: { $sum: "$actualHours" },
          averageTaskDuration: {
            $avg: {
              $cond: [
                { $eq: ["$status", "completed"] },
                {
                  $divide: [
                    { $subtract: ["$completedDate", "$startDate"] },
                    1000 * 60 * 60, // Convert to hours
                  ],
                },
                null,
              ],
            },
          },
        },
      },
    ])

    const employee = await Employee.findById(filter.assignedTo)
      .populate("department", "name")
      .select("name email username")

    const performance = performanceData[0] || {
      totalTasks: 0,
      completedTasks: 0,
      onTimeTasks: 0,
      totalEstimatedHours: 0,
      totalActualHours: 0,
      averageTaskDuration: 0,
    }

    res.json({
      success: true,
      employee,
      performance: {
        ...performance,
        completionRate: performance.totalTasks > 0 
          ? Math.round((performance.completedTasks / performance.totalTasks) * 100) 
          : 0,
        onTimeRate: performance.completedTasks > 0 
          ? Math.round((performance.onTimeTasks / performance.completedTasks) * 100) 
          : 0,
        efficiencyRate: performance.totalEstimatedHours > 0 
          ? Math.round((performance.totalEstimatedHours / performance.totalActualHours) * 100) 
          : 0,
      },
      reportPeriod: {
        startDate,
        endDate,
        generatedAt: new Date(),
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}
