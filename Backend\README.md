# TaskManager - Time Tracking System

A comprehensive time tracking system with role-based access control, email verification, and detailed reporting capabilities.

## Features

### Role-Based System
- **SuperAdmin (Read-Write)**: Full system access, can create departments and admins
- **SuperAdmin (Read-Only)**: Can view all reports but cannot create/modify data
- **Admin**: Can manage tasks and employees within their department
- **Employee**: Can track time on assigned tasks and view their own performance

### Core Functionality
- **Department Management**: Create and manage organizational departments
- **User Management**: Role-based user creation and management
- **Task Management**: Create, assign, and track tasks with time logging
- **Time Tracking**: Start/stop time tracking, manual time entries
- **Email Verification**: OTP-based email verification for employee registration
- **Comprehensive Reporting**: Task reports, employee performance, department analytics

## Setup Instructions

### 1. Environment Configuration
Copy the example environment file and configure your settings:
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
- MongoDB connection string
- JWT secret key
- Email service credentials (Gmail recommended)
- Frontend URL

### 2. Install Dependencies
```bash
npm install
```

### 3. <PERSON><PERSON> SuperAdmin Accounts
Run the seeding script to create 3 superadmin accounts:
```bash
npm run seed-superadmins
```

This creates:
- **superadmin_rw** (Read-Write permissions)
- **superadmin_ro1** (Read-Only permissions)
- **superadmin_ro2** (Read-Only permissions)

Default password: `SuperAdmin123!`

### 4. Start the Server
```bash
# Development mode
npm run dev

# Production mode
npm start
```

## API Endpoints

### Authentication
- `POST /api/auth/superadmin/login` - SuperAdmin login
- `POST /api/auth/admin/login` - Admin login
- `POST /api/auth/employee/register` - Employee registration (sends OTP)
- `POST /api/auth/employee/verify-email` - Verify email with OTP
- `POST /api/auth/employee/login` - Employee login

### Department Management
- `POST /api/department/create` - Create department (SuperAdmin Read-Write only)
- `GET /api/department/all` - Get all departments
- `PUT /api/department/:id` - Update department (SuperAdmin Read-Write only)
- `PUT /api/department/:id/toggle-status` - Toggle department status

### Admin Management
- `POST /api/admin/create` - Create admin (SuperAdmin Read-Write only)
- `GET /api/admin/all` - Get all admins (Any SuperAdmin)
- `PUT /api/admin/:id/password` - Update admin password
- `PUT /api/admin/:id/toggle-status` - Toggle admin status

### Task Management
- `POST /api/task/create` - Create task (Admin/SuperAdmin)
- `GET /api/task/all` - Get tasks (filtered by role)
- `GET /api/task/:id` - Get task details
- `PUT /api/task/:id` - Update task
- `DELETE /api/task/:id` - Delete task (Admin/SuperAdmin)

### Time Tracking
- `POST /api/task/:id/start-tracking` - Start time tracking (Employee)
- `POST /api/task/:id/stop-tracking` - Stop time tracking (Employee)
- `POST /api/task/:id/add-time-entry` - Add manual time entry (Employee)
- `GET /api/task/:id/time-tracking` - Get time tracking details

### Reporting
- `GET /api/task/reports/tasks` - Comprehensive task reports (SuperAdmin)
- `GET /api/task/reports/employee-performance` - Employee performance reports

## User Workflows

### SuperAdmin (Read-Write) Workflow
1. Login with `superadmin_rw` credentials
2. Create departments via `/api/department/create`
3. Create admins and assign them to departments
4. Access all reports and analytics
5. Monitor system-wide performance

### SuperAdmin (Read-Only) Workflow
1. Login with `superadmin_ro1` or `superadmin_ro2` credentials
2. View all reports and analytics
3. Monitor department and employee performance
4. Cannot create or modify data

### Admin Workflow
1. Login with admin credentials
2. Create and assign tasks to employees in their department
3. Monitor department task progress
4. View department-specific reports
5. Manage employee task assignments

### Employee Workflow
1. Register using company email
2. Verify email with OTP sent to inbox
3. Login and view assigned tasks
4. Start/stop time tracking on tasks
5. Add manual time entries if needed
6. Update task status and add comments
7. View personal performance reports

## Email Configuration

For Gmail, you need to:
1. Enable 2-factor authentication
2. Generate an App Password
3. Use the App Password in `EMAIL_PASSWORD`

Example Gmail configuration:
```env
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-16-character-app-password
```

## Database Schema

### Collections
- **users**: SuperAdmins and Admins
- **employees**: Employee accounts with email verification
- **departments**: Organizational departments
- **tasks**: Tasks with time tracking and metadata

### Key Features
- Email verification with OTP expiration
- Time tracking with start/stop functionality
- Comprehensive task metadata (comments, attachments, tags)
- Role-based data access and filtering
- Soft delete for data integrity

## Security Features

- JWT-based authentication
- Role-based access control
- Email verification for employee accounts
- Password hashing with bcrypt
- Input validation and sanitization
- Secure time tracking with ownership validation

## Performance Considerations

- Database indexes for common queries
- Pagination for large datasets
- Aggregation pipelines for reporting
- Efficient time tracking calculations
- Optimized role-based filtering

## Development

### Adding New Features
1. Create models in `/models`
2. Implement controllers in `/controllers`
3. Define routes in `/routes`
4. Add middleware for authentication/authorization
5. Update documentation

### Testing
- Test all role-based access controls
- Verify email verification flow
- Test time tracking accuracy
- Validate reporting calculations
- Check permission boundaries

## Support

For issues or questions:
1. Check the API documentation
2. Verify environment configuration
3. Ensure database connectivity
4. Check email service setup
5. Review role permissions
