import Task from "../models/Task.js"
import Employee from "../models/Employee.js"
import Department from "../models/Department.js"

// Create a new task (Admin/SuperAdmin only)
export const createTask = async (req, res) => {
  try {
    const {
      title,
      description,
      assignedTo,
      department,
      dueDate,
      estimatedHours,
      priority,
      tags,
    } = req.body

    // Validate required fields
    if (!title || !assignedTo || !department) {
      return res.status(400).json({
        success: false,
        message: "Title, assigned employee, and department are required",
      })
    }

    // Verify employee exists and belongs to the department
    const employee = await Employee.findById(assignedTo).populate("department")
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: "Employee not found",
      })
    }

    if (employee.department._id.toString() !== department) {
      return res.status(400).json({
        success: false,
        message: "Employee does not belong to the specified department",
      })
    }

    // For admins, ensure they can only assign tasks in their department
    if (req.user.role === "admin" && req.user.department.toString() !== department) {
      return res.status(403).json({
        success: false,
        message: "You can only assign tasks within your department",
      })
    }

    const task = new Task({
      title,
      description,
      assignedTo,
      assignedBy: req.user.id,
      department,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      estimatedHours: estimatedHours || 0,
      priority: priority || "medium",
      tags: tags || [],
    })

    await task.save()
    await task.populate([
      { path: "assignedTo", select: "name email username" },
      { path: "assignedBy", select: "name email username" },
      { path: "department", select: "name description" },
    ])

    res.status(201).json({
      success: true,
      message: "Task created successfully",
      task,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Get all tasks with filtering and pagination
export const getAllTasks = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      priority,
      department,
      assignedTo,
      assignedBy,
      search,
    } = req.query

    // Build filter object
    const filter = { isActive: true }

    // Role-based filtering
    if (req.user.role === "admin") {
      filter.department = req.user.department
    } else if (req.user.role === "employee") {
      filter.assignedTo = req.user.id
    }

    // Apply additional filters
    if (status) filter.status = status
    if (priority) filter.priority = priority
    if (department && req.user.role === "superadmin") filter.department = department
    if (assignedTo && req.user.role !== "employee") filter.assignedTo = assignedTo
    if (assignedBy) filter.assignedBy = assignedBy

    // Search functionality
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
        { tags: { $in: [new RegExp(search, "i")] } },
      ]
    }

    const skip = (page - 1) * limit
    const tasks = await Task.find(filter)
      .populate("assignedTo", "name email username")
      .populate("assignedBy", "name email username")
      .populate("department", "name description")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))

    const total = await Task.countDocuments(filter)

    res.json({
      success: true,
      tasks,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalTasks: total,
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Get task by ID
export const getTaskById = async (req, res) => {
  try {
    const { taskId } = req.params

    const task = await Task.findById(taskId)
      .populate("assignedTo", "name email username contactNumber")
      .populate("assignedBy", "name email username")
      .populate("department", "name description")
      .populate("comments.author")

    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Check permissions
    if (req.user.role === "employee" && task.assignedTo._id.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "Access denied. You can only view your own tasks.",
      })
    }

    if (req.user.role === "admin" && task.department._id.toString() !== req.user.department.toString()) {
      return res.status(403).json({
        success: false,
        message: "Access denied. You can only view tasks in your department.",
      })
    }

    res.json({
      success: true,
      task,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Update task
export const updateTask = async (req, res) => {
  try {
    const { taskId } = req.params
    const updates = req.body

    const task = await Task.findById(taskId)
    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Check permissions
    if (req.user.role === "employee") {
      // Employees can only update status and add comments
      const allowedUpdates = ["status", "comments"]
      const updateKeys = Object.keys(updates)
      const isValidUpdate = updateKeys.every(key => allowedUpdates.includes(key))
      
      if (!isValidUpdate || task.assignedTo.toString() !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: "Access denied. Employees can only update status of their own tasks.",
        })
      }
    } else if (req.user.role === "admin" && task.department.toString() !== req.user.department.toString()) {
      return res.status(403).json({
        success: false,
        message: "Access denied. You can only update tasks in your department.",
      })
    }

    // Handle status updates
    if (updates.status === "completed" && task.status !== "completed") {
      await task.markCompleted()
    } else {
      Object.keys(updates).forEach(key => {
        if (key !== "status" || updates.status !== "completed") {
          task[key] = updates[key]
        }
      })
      await task.save()
    }

    await task.populate([
      { path: "assignedTo", select: "name email username" },
      { path: "assignedBy", select: "name email username" },
      { path: "department", select: "name description" },
    ])

    res.json({
      success: true,
      message: "Task updated successfully",
      task,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Delete task (Admin/SuperAdmin only)
export const deleteTask = async (req, res) => {
  try {
    const { taskId } = req.params

    const task = await Task.findById(taskId)
    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Check permissions
    if (req.user.role === "admin" && task.department.toString() !== req.user.department.toString()) {
      return res.status(403).json({
        success: false,
        message: "Access denied. You can only delete tasks in your department.",
      })
    }

    // Soft delete
    task.isActive = false
    await task.save()

    res.json({
      success: true,
      message: "Task deleted successfully",
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Start time tracking for a task
export const startTimeTracking = async (req, res) => {
  try {
    const { taskId } = req.params
    const { description } = req.body

    const task = await Task.findById(taskId)
    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Only assigned employee can track time
    if (task.assignedTo.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only track time for tasks assigned to you",
      })
    }

    await task.startTimeTracking(description)

    res.json({
      success: true,
      message: "Time tracking started successfully",
      task: {
        id: task._id,
        title: task.title,
        status: task.status,
        activeTimeEntry: task.timeEntries[task.timeEntries.length - 1],
      },
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    })
  }
}

// Stop time tracking for a task
export const stopTimeTracking = async (req, res) => {
  try {
    const { taskId } = req.params

    const task = await Task.findById(taskId)
    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Only assigned employee can track time
    if (task.assignedTo.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only track time for tasks assigned to you",
      })
    }

    await task.stopTimeTracking()

    res.json({
      success: true,
      message: "Time tracking stopped successfully",
      task: {
        id: task._id,
        title: task.title,
        totalTimeSpent: task.totalTimeSpent,
        actualHours: task.actualHours,
        lastTimeEntry: task.timeEntries[task.timeEntries.length - 1],
      },
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    })
  }
}

// Add manual time entry
export const addTimeEntry = async (req, res) => {
  try {
    const { taskId } = req.params
    const { startTime, endTime, description } = req.body

    if (!startTime || !endTime) {
      return res.status(400).json({
        success: false,
        message: "Start time and end time are required",
      })
    }

    const task = await Task.findById(taskId)
    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Only assigned employee can add time entries
    if (task.assignedTo.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only add time entries for tasks assigned to you",
      })
    }

    await task.addTimeEntry(startTime, endTime, description)

    res.json({
      success: true,
      message: "Time entry added successfully",
      task: {
        id: task._id,
        title: task.title,
        totalTimeSpent: task.totalTimeSpent,
        actualHours: task.actualHours,
        newTimeEntry: task.timeEntries[task.timeEntries.length - 1],
      },
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    })
  }
}

// Get time tracking summary for a task
export const getTaskTimeTracking = async (req, res) => {
  try {
    const { taskId } = req.params

    const task = await Task.findById(taskId)
      .populate("assignedTo", "name email username")
      .select("title status estimatedHours actualHours timeEntries totalTimeSpent progressPercentage")

    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Check permissions
    if (req.user.role === "employee" && task.assignedTo._id.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "Access denied",
      })
    }

    res.json({
      success: true,
      timeTracking: {
        taskId: task._id,
        title: task.title,
        assignedTo: task.assignedTo,
        status: task.status,
        estimatedHours: task.estimatedHours,
        actualHours: task.actualHours,
        totalTimeSpent: task.totalTimeSpent, // in minutes
        progressPercentage: task.progressPercentage,
        timeEntries: task.timeEntries,
        isActiveTracking: task.timeEntries.some(entry => !entry.endTime),
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}
