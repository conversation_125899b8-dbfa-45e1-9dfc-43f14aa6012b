import Employee from "../models/Employee.js"

// Test endpoint to create employee without email verification (for testing only)
export const createTestEmployee = async (req, res) => {
  try {
    const { name, email, contactNumber, address, username, password, department } = req.body

    // Check if employee already exists
    const existingEmployee = await Employee.findOne({
      $or: [{ email }, { username }],
    })

    if (existingEmployee) {
      return res.status(400).json({
        success: false,
        message: "Employee with this email or username already exists",
      })
    }

    // Create employee with verification bypassed (for testing)
    const employee = new Employee({
      name,
      email,
      contactNumber,
      address,
      username,
      password,
      department,
      isActive: true, // Active immediately for testing
      isEmailVerified: true, // Verified immediately for testing
    })

    await employee.save()

    res.status(201).json({
      success: true,
      message: "Test employee created successfully (email verification bypassed)",
      employee: {
        id: employee._id,
        name: employee.name,
        email: employee.email,
        username: employee.username,
        contactNumber: employee.contactNumber,
        address: employee.address,
        department: employee.department,
        isActive: employee.isActive,
        isEmailVerified: employee.isEmailVerified,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}
