import axios from 'axios';

const BASE_URL = 'http://localhost:5000/api';

// Test data
const testData = {
  superAdminRW: {
    username: 'superadmin_rw',
    password: 'SuperAdmin123!'
  },
  superAdminRO1: {
    username: 'superadmin_ro1',
    password: 'SuperAdmin123!'
  }
};

let tokens = {};
let createdData = {};

async function testTaskAPI() {
  console.log('🚀 Starting Task Management & Time Tracking Tests...\n');

  try {
    // Setup: Login as SuperAdmin and get existing data
    console.log('🔧 Setup: Logging in and getting existing data...');
    const superAdminLogin = await axios.post(`${BASE_URL}/auth/superadmin/login`, testData.superAdminRW);
    tokens.superAdminRW = superAdminLogin.data.token;

    const superAdminROLogin = await axios.post(`${BASE_URL}/auth/superadmin/login`, testData.superAdminRO1);
    tokens.superAdminRO1 = superAdminROLogin.data.token;

    // Get departments
    const departmentsResponse = await axios.get(`${BASE_URL}/department/all`);
    const department = departmentsResponse.data.departments[0];
    createdData.department = department;

    // Get employees
    const employeesResponse = await axios.get(`${BASE_URL}/employee/all`, {
      headers: { Authorization: `Bearer ${tokens.superAdminRW}` }
    });
    const employee = employeesResponse.data.employees[0];
    createdData.employee = employee;

    // Get admins
    const adminsResponse = await axios.get(`${BASE_URL}/admin/all`, {
      headers: { Authorization: `Bearer ${tokens.superAdminRW}` }
    });
    const admin = adminsResponse.data.admins[0];
    createdData.admin = admin;

    console.log(`✅ Setup complete - Using department: ${department.name}, employee: ${employee.name}\n`);

    // Test 1: Create Task (SuperAdmin)
    console.log('1️⃣ Testing Task Creation by SuperAdmin...');
    const taskData = {
      title: 'Implement User Authentication',
      description: 'Create login and registration functionality with JWT tokens',
      assignedTo: employee._id,
      department: department._id,
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      estimatedHours: 8,
      priority: 'high',
      tags: ['authentication', 'security', 'backend']
    };

    const taskResponse = await axios.post(`${BASE_URL}/task/create`, taskData, {
      headers: { Authorization: `Bearer ${tokens.superAdminRW}` }
    });
    createdData.task = taskResponse.data.task;
    console.log('✅ Task created successfully');
    console.log(`   Task: ${createdData.task.title}`);
    console.log(`   Assigned to: ${createdData.task.assignedTo.name}`);
    console.log(`   Department: ${createdData.task.department.name}`);
    console.log(`   Priority: ${createdData.task.priority}\n`);

    // Test 2: Get All Tasks
    console.log('2️⃣ Testing Get All Tasks...');
    const allTasksResponse = await axios.get(`${BASE_URL}/task/all`, {
      headers: { Authorization: `Bearer ${tokens.superAdminRW}` }
    });
    console.log('✅ Retrieved all tasks');
    console.log(`   Total tasks: ${allTasksResponse.data.tasks.length}`);
    console.log(`   Current page: ${allTasksResponse.data.pagination.currentPage}`);
    console.log(`   Total pages: ${allTasksResponse.data.pagination.totalPages}\n`);

    // Test 3: Get Task by ID
    console.log('3️⃣ Testing Get Task by ID...');
    const taskByIdResponse = await axios.get(`${BASE_URL}/task/${createdData.task._id}`, {
      headers: { Authorization: `Bearer ${tokens.superAdminRW}` }
    });
    console.log('✅ Retrieved task by ID');
    console.log(`   Task: ${taskByIdResponse.data.task.title}`);
    console.log(`   Status: ${taskByIdResponse.data.task.status}`);
    console.log(`   Estimated Hours: ${taskByIdResponse.data.task.estimatedHours}\n`);

    // Test 4: Login as Employee for Time Tracking
    console.log('4️⃣ Testing Employee Login for Time Tracking...');
    // We need to find the employee's username and password from our test data
    // Since we created the employee via test endpoint, we need to get the credentials
    
    // For this test, let's create a new employee with known credentials
    const testEmployeeData = {
      name: 'Test Employee for Time Tracking',
      email: `timetrack.employee.${Date.now()}@company.com`,
      contactNumber: '+1234567890',
      address: '123 Test St',
      username: `timetrack_${Date.now()}`,
      password: 'TimeTrack123!',
      department: department._id
    };

    const newEmployeeResponse = await axios.post(`${BASE_URL}/test/create-employee`, testEmployeeData);
    const timeTrackEmployee = newEmployeeResponse.data.employee;

    const employeeLoginResponse = await axios.post(`${BASE_URL}/auth/employee/login`, {
      username: testEmployeeData.username,
      password: testEmployeeData.password
    });
    tokens.employee = employeeLoginResponse.data.token;
    console.log('✅ Employee logged in for time tracking');
    console.log(`   Employee: ${employeeLoginResponse.data.user.name}\n`);

    // Test 5: Create Task for Time Tracking Employee
    console.log('5️⃣ Creating Task for Time Tracking Employee...');
    const timeTrackTaskData = {
      title: 'Test Time Tracking Feature',
      description: 'Test the time tracking functionality',
      assignedTo: timeTrackEmployee.id,
      department: department._id,
      estimatedHours: 4,
      priority: 'medium'
    };

    const timeTrackTaskResponse = await axios.post(`${BASE_URL}/task/create`, timeTrackTaskData, {
      headers: { Authorization: `Bearer ${tokens.superAdminRW}` }
    });
    createdData.timeTrackTask = timeTrackTaskResponse.data.task;
    console.log('✅ Time tracking task created');
    console.log(`   Task: ${createdData.timeTrackTask.title}\n`);

    // Test 6: Start Time Tracking
    console.log('6️⃣ Testing Start Time Tracking...');
    console.log(`   Debug: Task assigned to: ${createdData.timeTrackTask.assignedTo._id}`);
    console.log(`   Debug: Employee ID: ${timeTrackEmployee.id}`);
    console.log(`   Debug: Employee from login: ${employeeLoginResponse.data.user.id}`);

    const startTrackingResponse = await axios.post(
      `${BASE_URL}/task/${createdData.timeTrackTask._id}/start-tracking`,
      { description: 'Starting work on authentication feature' },
      { headers: { Authorization: `Bearer ${tokens.employee}` } }
    );
    console.log('✅ Time tracking started');
    console.log(`   Task: ${startTrackingResponse.data.task.title}`);
    console.log(`   Status: ${startTrackingResponse.data.task.status}`);
    console.log(`   Active entry started at: ${startTrackingResponse.data.task.activeTimeEntry.startTime}\n`);

    // Wait a few seconds to simulate work
    console.log('⏳ Simulating work for 3 seconds...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test 7: Stop Time Tracking
    console.log('7️⃣ Testing Stop Time Tracking...');
    const stopTrackingResponse = await axios.post(
      `${BASE_URL}/task/${createdData.timeTrackTask._id}/stop-tracking`,
      {},
      { headers: { Authorization: `Bearer ${tokens.employee}` } }
    );
    console.log('✅ Time tracking stopped');
    console.log(`   Task: ${stopTrackingResponse.data.task.title}`);
    console.log(`   Total time spent: ${stopTrackingResponse.data.task.totalTimeSpent} minutes`);
    console.log(`   Actual hours: ${stopTrackingResponse.data.task.actualHours}\n`);

    // Test 8: Add Manual Time Entry
    console.log('8️⃣ Testing Manual Time Entry...');
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    
    const manualTimeResponse = await axios.post(
      `${BASE_URL}/task/${createdData.timeTrackTask._id}/add-time-entry`,
      {
        startTime: oneHourAgo.toISOString(),
        endTime: now.toISOString(),
        description: 'Manual time entry for documentation work'
      },
      { headers: { Authorization: `Bearer ${tokens.employee}` } }
    );
    console.log('✅ Manual time entry added');
    console.log(`   Duration: ${manualTimeResponse.data.task.newTimeEntry.duration} minutes`);
    console.log(`   Total time spent: ${manualTimeResponse.data.task.totalTimeSpent} minutes\n`);

    // Test 9: Get Time Tracking Summary
    console.log('9️⃣ Testing Get Time Tracking Summary...');
    const timeTrackingSummaryResponse = await axios.get(
      `${BASE_URL}/task/${createdData.timeTrackTask._id}/time-tracking`,
      { headers: { Authorization: `Bearer ${tokens.employee}` } }
    );
    const timeTracking = timeTrackingSummaryResponse.data.timeTracking;
    console.log('✅ Time tracking summary retrieved');
    console.log(`   Task: ${timeTracking.title}`);
    console.log(`   Estimated Hours: ${timeTracking.estimatedHours}`);
    console.log(`   Actual Hours: ${timeTracking.actualHours}`);
    console.log(`   Progress: ${timeTracking.progressPercentage}%`);
    console.log(`   Time Entries: ${timeTracking.timeEntries.length}\n`);

    // Test 10: Update Task Status
    console.log('🔟 Testing Task Status Update...');
    const updateTaskResponse = await axios.put(
      `${BASE_URL}/task/${createdData.timeTrackTask._id}`,
      { status: 'completed' },
      { headers: { Authorization: `Bearer ${tokens.employee}` } }
    );
    console.log('✅ Task status updated to completed');
    console.log(`   Task: ${updateTaskResponse.data.task.title}`);
    console.log(`   Status: ${updateTaskResponse.data.task.status}`);
    console.log(`   Completed Date: ${updateTaskResponse.data.task.completedDate}\n`);

    // Test 11: Get Task Reports (SuperAdmin)
    console.log('1️⃣1️⃣ Testing Task Reports...');
    const reportsResponse = await axios.get(
      `${BASE_URL}/task/reports/tasks?reportType=summary`,
      { headers: { Authorization: `Bearer ${tokens.superAdminRW}` } }
    );
    const report = reportsResponse.data.report;
    console.log('✅ Task reports retrieved');
    console.log(`   Total Tasks: ${report.summary.totalTasks}`);
    console.log(`   Completed Tasks: ${report.summary.completedTasks}`);
    console.log(`   Completion Rate: ${report.completionRate}%`);
    console.log(`   Department Breakdown: ${report.departmentBreakdown.length} departments\n`);

    // Test 12: Get Employee Performance Report
    console.log('1️⃣2️⃣ Testing Employee Performance Report...');
    const performanceResponse = await axios.get(
      `${BASE_URL}/task/reports/employee-performance?employeeId=${timeTrackEmployee.id}`,
      { headers: { Authorization: `Bearer ${tokens.superAdminRW}` } }
    );
    const performance = performanceResponse.data.performance;
    console.log('✅ Employee performance report retrieved');
    console.log(`   Employee: ${performanceResponse.data.employee.name}`);
    console.log(`   Total Tasks: ${performance.totalTasks}`);
    console.log(`   Completed Tasks: ${performance.completedTasks}`);
    console.log(`   Completion Rate: ${performance.completionRate}%`);
    console.log(`   Efficiency Rate: ${performance.efficiencyRate}%\n`);

    console.log('🎉 All Task Management & Time Tracking tests completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('✅ Task creation by SuperAdmin');
    console.log('✅ Task retrieval and filtering');
    console.log('✅ Employee time tracking (start/stop)');
    console.log('✅ Manual time entry addition');
    console.log('✅ Time tracking summary and progress');
    console.log('✅ Task status updates');
    console.log('✅ Comprehensive reporting system');
    console.log('✅ Employee performance analytics');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the tests
testTaskAPI();
