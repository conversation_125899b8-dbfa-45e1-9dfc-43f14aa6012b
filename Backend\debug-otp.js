import axios from 'axios';

const BASE_URL = 'http://localhost:5000/api';

async function debugOTP() {
  console.log('🔍 Debugging OTP Issue...\n');

  try {
    // Get departments
    const departmentsResponse = await axios.get(`${BASE_URL}/department/all`);
    const department = departmentsResponse.data.departments[0];

    // Register employee
    console.log('1️⃣ Registering employee...');
    const employeeData = {
      name: 'Debug OTP Test',
      email: `debug.otp.${Date.now()}@company.com`,
      contactNumber: '+1234567890',
      address: '123 Debug St',
      username: `debugotp_${Date.now()}`,
      password: 'DebugOTP123!',
      department: department._id
    };

    const registrationResponse = await axios.post(`${BASE_URL}/auth/employee/register`, employeeData);
    console.log('Registration response:', JSON.stringify(registrationResponse.data, null, 2));

    // Try verification with 123456
    console.log('\n2️⃣ Trying verification with 123456...');
    try {
      const verificationResponse = await axios.post(`${BASE_URL}/auth/employee/verify-email`, {
        employeeId: registrationResponse.data.data.employeeId,
        otp: '123456'
      });
      console.log('Verification successful:', JSON.stringify(verificationResponse.data, null, 2));
    } catch (error) {
      console.log('Verification failed:', JSON.stringify(error.response.data, null, 2));
    }

  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

debugOTP();
