import mongoose from "mongoose"
import dotenv from "dotenv"
import User from "../models/User.js"

dotenv.config()

const createSuperAdmins = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI)

    // Check if any superadmins already exist
    const existingSuperAdmins = await User.find({ role: "superadmin" })
    if (existingSuperAdmins.length > 0) {
      console.log("Super admins already exist")
      console.log(`Found ${existingSuperAdmins.length} existing super admin(s)`)
      process.exit(0)
    }

    // Create 3 superadmins as specified
    const superAdmins = [
      {
        name: "Super Administrator (Read-Write)",
        email: "<EMAIL>",
        username: "superadmin_rw",
        password: "SuperAdmin123!",
        role: "superadmin",
        permissions: "read-write",
      },
      {
        name: "Super Administrator (Read-Only 1)",
        email: "<EMAIL>",
        username: "superadmin_ro1",
        password: "SuperAdmin123!",
        role: "superadmin",
        permissions: "read-only",
      },
      {
        name: "Super Administrator (Read-Only 2)",
        email: "<EMAIL>",
        username: "superadmin_ro2",
        password: "SuperAdmin123!",
        role: "superadmin",
        permissions: "read-only",
      },
    ]

    console.log("Creating 3 super administrators...")

    for (const adminData of superAdmins) {
      const superAdmin = new User(adminData)
      await superAdmin.save()
      console.log(`✓ Created: ${adminData.name}`)
      console.log(`  Username: ${adminData.username}`)
      console.log(`  Email: ${adminData.email}`)
      console.log(`  Permissions: ${adminData.permissions}`)
      console.log("")
    }

    console.log("All super administrators created successfully!")
    console.log("Default password for all accounts: SuperAdmin123!")
    console.log("")
    console.log("Login credentials:")
    console.log("1. Read-Write SuperAdmin: superadmin_rw / SuperAdmin123!")
    console.log("2. Read-Only SuperAdmin 1: superadmin_ro1 / SuperAdmin123!")
    console.log("3. Read-Only SuperAdmin 2: superadmin_ro2 / SuperAdmin123!")

    process.exit(0)
  } catch (error) {
    console.error("Error creating super admins:", error)
    process.exit(1)
  }
}

createSuperAdmins()
