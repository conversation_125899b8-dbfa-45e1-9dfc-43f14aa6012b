import mongoose from "mongoose"

const taskSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    status: {
      type: String,
      enum: ["pending", "in-progress", "completed", "cancelled"],
      default: "pending",
    },
    priority: {
      type: String,
      enum: ["low", "medium", "high", "urgent"],
      default: "medium",
    },
    assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employee",
      required: true,
    },
    assignedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User", // Admin or SuperAdmin who assigned the task
      required: true,
    },
    department: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Department",
      required: true,
    },
    dueDate: {
      type: Date,
    },
    startDate: {
      type: Date,
    },
    completedDate: {
      type: Date,
    },
    // Time tracking fields
    estimatedHours: {
      type: Number,
      min: 0,
      default: 0,
    },
    actualHours: {
      type: Number,
      min: 0,
      default: 0,
    },
    timeEntries: [
      {
        startTime: {
          type: Date,
          required: true,
        },
        endTime: {
          type: Date,
        },
        duration: {
          type: Number, // Duration in minutes
          min: 0,
        },
        description: {
          type: String,
          trim: true,
          maxlength: 500,
        },
        createdAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    // Task metadata
    tags: [
      {
        type: String,
        trim: true,
        maxlength: 50,
      },
    ],
    attachments: [
      {
        filename: {
          type: String,
          required: true,
        },
        originalName: {
          type: String,
          required: true,
        },
        mimeType: {
          type: String,
          required: true,
        },
        size: {
          type: Number,
          required: true,
        },
        uploadedAt: {
          type: Date,
          default: Date.now,
        },
        uploadedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Employee",
          required: true,
        },
      },
    ],
    comments: [
      {
        text: {
          type: String,
          required: true,
          trim: true,
          maxlength: 1000,
        },
        author: {
          type: mongoose.Schema.Types.ObjectId,
          required: true,
          refPath: "comments.authorModel",
        },
        authorModel: {
          type: String,
          required: true,
          enum: ["User", "Employee"],
        },
        createdAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Index for better query performance
taskSchema.index({ assignedTo: 1, status: 1 })
taskSchema.index({ department: 1, status: 1 })
taskSchema.index({ assignedBy: 1, createdAt: -1 })
taskSchema.index({ dueDate: 1, status: 1 })

// Virtual for total time spent
taskSchema.virtual("totalTimeSpent").get(function () {
  return this.timeEntries.reduce((total, entry) => {
    return total + (entry.duration || 0)
  }, 0)
})

// Virtual for task progress percentage
taskSchema.virtual("progressPercentage").get(function () {
  if (this.status === "completed") return 100
  if (this.status === "cancelled") return 0
  if (this.estimatedHours === 0) return 0
  
  const hoursSpent = this.totalTimeSpent / 60 // Convert minutes to hours
  return Math.min(Math.round((hoursSpent / this.estimatedHours) * 100), 100)
})

// Method to add time entry
taskSchema.methods.addTimeEntry = function (startTime, endTime, description) {
  const start = new Date(startTime)
  const end = new Date(endTime)
  
  if (end <= start) {
    throw new Error("End time must be after start time")
  }
  
  const duration = Math.round((end - start) / (1000 * 60)) // Duration in minutes
  
  this.timeEntries.push({
    startTime: start,
    endTime: end,
    duration,
    description: description || "",
  })
  
  // Update actual hours
  this.actualHours = this.totalTimeSpent / 60
  
  return this.save()
}

// Method to start time tracking
taskSchema.methods.startTimeTracking = function (description) {
  // Check if there's already an active time entry
  const activeEntry = this.timeEntries.find(entry => !entry.endTime)
  if (activeEntry) {
    throw new Error("Time tracking is already active for this task")
  }
  
  this.timeEntries.push({
    startTime: new Date(),
    description: description || "",
  })
  
  // Update status to in-progress if it's pending
  if (this.status === "pending") {
    this.status = "in-progress"
    if (!this.startDate) {
      this.startDate = new Date()
    }
  }
  
  return this.save()
}

// Method to stop time tracking
taskSchema.methods.stopTimeTracking = function () {
  const activeEntry = this.timeEntries.find(entry => !entry.endTime)
  if (!activeEntry) {
    throw new Error("No active time tracking found for this task")
  }
  
  const endTime = new Date()
  const duration = Math.round((endTime - activeEntry.startTime) / (1000 * 60))
  
  activeEntry.endTime = endTime
  activeEntry.duration = duration
  
  // Update actual hours
  this.actualHours = this.totalTimeSpent / 60
  
  return this.save()
}

// Method to mark task as completed
taskSchema.methods.markCompleted = async function () {
  // Stop any active time tracking
  const activeEntry = this.timeEntries.find(entry => !entry.endTime)
  if (activeEntry) {
    const endTime = new Date()
    const duration = Math.round((endTime - activeEntry.startTime) / (1000 * 60))

    activeEntry.endTime = endTime
    activeEntry.duration = duration

    // Update actual hours
    this.actualHours = this.totalTimeSpent / 60
  }

  this.status = "completed"
  this.completedDate = new Date()

  return this.save()
}

// Ensure virtuals are included in JSON output
taskSchema.set("toJSON", { virtuals: true })
taskSchema.set("toObject", { virtuals: true })

export default mongoose.model("Task", taskSchema)
