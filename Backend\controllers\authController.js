import jwt from "jsonwebtoken"
import User from "../models/User.js"
import Employee from "../models/Employee.js"

const generateToken = (id, role) => {
  return jwt.sign({ id, role }, process.env.JWT_SECRET, {
    expiresIn: "30d",
  })
}

export const superAdminLogin = async (req, res) => {
  try {
    const { username, password } = req.body

    const superAdmin = await User.findOne({
      username,
      role: "superadmin",
      isActive: true,
    })

    if (!superAdmin) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      })
    }

    const isPasswordValid = await superAdmin.comparePassword(password)
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      })
    }

    const token = generateToken(superAdmin._id, superAdmin.role)

    res.json({
      success: true,
      message: "Login successful",
      token,
      user: {
        id: superAdmin._id,
        name: superAdmin.name,
        email: superAdmin.email,
        username: superAdmin.username,
        role: superAdmin.role,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const adminLogin = async (req, res) => {
  try {
    const { username, password } = req.body

    const admin = await User.findOne({
      username,
      role: "admin",
      isActive: true,
    }).populate("department")

    if (!admin) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      })
    }

    const isPasswordValid = await admin.comparePassword(password)
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      })
    }

    const token = generateToken(admin._id, admin.role)

    res.json({
      success: true,
      message: "Login successful",
      token,
      user: {
        id: admin._id,
        name: admin.name,
        email: admin.email,
        username: admin.username,
        role: admin.role,
        department: admin.department,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const employeeRegister = async (req, res) => {
  try {
    const { name, email, contactNumber, address, username, password, department } = req.body

    const existingEmployee = await Employee.findOne({
      $or: [{ email }, { username }],
    })

    if (existingEmployee) {
      return res.status(400).json({
        success: false,
        message: "Employee with this email or username already exists",
      })
    }

    const employee = new Employee({
      name,
      email,
      contactNumber,
      address,
      username,
      password,
      department,
    })

    await employee.save()

    res.status(201).json({
      success: true,
      message: "Employee registered successfully",
      employee: {
        id: employee._id,
        name: employee.name,
        email: employee.email,
        username: employee.username,
        contactNumber: employee.contactNumber,
        address: employee.address,
        department: employee.department,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const employeeLogin = async (req, res) => {
  try {
    const { username, password } = req.body

    const employee = await Employee.findOne({
      username,
      isActive: true,
    }).populate("department")

    if (!employee) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      })
    }

    const isPasswordValid = await employee.comparePassword(password)
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      })
    }

    const token = generateToken(employee._id, "employee")

    res.json({
      success: true,
      message: "Login successful",
      token,
      user: {
        id: employee._id,
        name: employee.name,
        email: employee.email,
        username: employee.username,
        contactNumber: employee.contactNumber,
        address: employee.address,
        role: "employee",
        department: employee.department,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}
