import jwt from "jsonwebtoken"
import User from "../models/User.js"
import Employee from "../models/Employee.js"
import { generateOTP, generateVerificationToken, sendOTPEmail, sendWelcomeEmail } from "../services/emailService.js"

const generateToken = (id, role) => {
  return jwt.sign({ id, role }, process.env.JWT_SECRET, {
    expiresIn: "30d",
  })
}

export const superAdminLogin = async (req, res) => {
  try {
    const { username, password } = req.body

    const superAdmin = await User.findOne({
      username,
      role: "superadmin",
      isActive: true,
    })

    if (!superAdmin) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      })
    }

    const isPasswordValid = await superAdmin.comparePassword(password)
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      })
    }

    const token = generateToken(superAdmin._id, superAdmin.role)

    res.json({
      success: true,
      message: "Login successful",
      token,
      user: {
        id: superAdmin._id,
        name: superAdmin.name,
        email: superAdmin.email,
        username: superAdmin.username,
        role: superAdmin.role,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const adminLogin = async (req, res) => {
  try {
    const { username, password } = req.body

    const admin = await User.findOne({
      username,
      role: "admin",
      isActive: true,
    }).populate("department")

    if (!admin) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      })
    }

    const isPasswordValid = await admin.comparePassword(password)
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      })
    }

    const token = generateToken(admin._id, admin.role)

    res.json({
      success: true,
      message: "Login successful",
      token,
      user: {
        id: admin._id,
        name: admin.name,
        email: admin.email,
        username: admin.username,
        role: admin.role,
        department: admin.department,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const employeeRegister = async (req, res) => {
  try {
    const { name, email, contactNumber, address, username, password, department } = req.body

    // Validate required fields
    if (!name || !email || !contactNumber || !address || !username || !password || !department) {
      return res.status(400).json({
        success: false,
        message: "All fields are required",
      })
    }

    // Check if employee already exists
    const existingEmployee = await Employee.findOne({
      $or: [{ email }, { username }],
    })

    if (existingEmployee) {
      return res.status(400).json({
        success: false,
        message: "Employee with this email or username already exists",
      })
    }

    // Generate OTP and verification token
    const otp = generateOTP()
    const verificationToken = generateVerificationToken()
    const verificationExpires = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

    // Create employee with verification fields
    const employee = new Employee({
      name,
      email,
      contactNumber,
      address,
      username,
      password,
      department,
      isActive: false, // Account inactive until email verification
      isEmailVerified: false,
      emailVerificationToken: verificationToken,
      emailVerificationExpires: verificationExpires,
    })

    await employee.save()

    // Send OTP email
    const emailResult = await sendOTPEmail(email, otp, name)

    if (!emailResult.success) {
      // If email fails, delete the created employee
      await Employee.findByIdAndDelete(employee._id)
      return res.status(500).json({
        success: false,
        message: "Failed to send verification email. Please try again.",
        error: emailResult.error,
      })
    }

    // Store OTP temporarily (in production, use Redis or similar)
    // For now, we'll store it in the employee document
    employee.emailVerificationToken = `${verificationToken}:${otp}`
    await employee.save()

    res.status(201).json({
      success: true,
      message: "Registration successful! Please check your email for the verification OTP.",
      data: {
        employeeId: employee._id,
        email: employee.email,
        message: "An OTP has been sent to your email address. Please verify your email to activate your account.",
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Verify employee email with OTP
export const verifyEmployeeEmail = async (req, res) => {
  try {
    const { employeeId, otp } = req.body

    if (!employeeId || !otp) {
      return res.status(400).json({
        success: false,
        message: "Employee ID and OTP are required",
      })
    }

    const employee = await Employee.findById(employeeId)
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: "Employee not found",
      })
    }

    // Check if already verified
    if (employee.isEmailVerified) {
      return res.status(400).json({
        success: false,
        message: "Email is already verified",
      })
    }

    // Check if verification token exists and hasn't expired
    if (!employee.emailVerificationToken || !employee.emailVerificationExpires) {
      return res.status(400).json({
        success: false,
        message: "No verification request found. Please register again.",
      })
    }

    if (employee.emailVerificationExpires < new Date()) {
      return res.status(400).json({
        success: false,
        message: "Verification OTP has expired. Please register again.",
      })
    }

    // Extract stored OTP from verification token
    const [token, storedOTP] = employee.emailVerificationToken.split(":")

    if (otp !== storedOTP) {
      return res.status(400).json({
        success: false,
        message: "Invalid OTP. Please check your email and try again.",
      })
    }

    // Verify email and activate account
    employee.isEmailVerified = true
    employee.isActive = true
    employee.emailVerificationToken = undefined
    employee.emailVerificationExpires = undefined
    await employee.save()

    // Send welcome email
    await sendWelcomeEmail(employee.email, employee.name, employee.username)

    res.json({
      success: true,
      message: "Email verified successfully! Your account is now active.",
      data: {
        employeeId: employee._id,
        name: employee.name,
        email: employee.email,
        username: employee.username,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

export const employeeLogin = async (req, res) => {
  try {
    const { username, password } = req.body

    const employee = await Employee.findOne({
      username,
      isActive: true,
      isEmailVerified: true, // Only allow verified employees to login
    }).populate("department")

    if (!employee) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials or account not verified",
      })
    }

    const isPasswordValid = await employee.comparePassword(password)
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      })
    }

    const token = generateToken(employee._id, "employee")

    res.json({
      success: true,
      message: "Login successful",
      token,
      user: {
        id: employee._id,
        name: employee.name,
        email: employee.email,
        username: employee.username,
        contactNumber: employee.contactNumber,
        address: employee.address,
        role: "employee",
        department: employee.department,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}
