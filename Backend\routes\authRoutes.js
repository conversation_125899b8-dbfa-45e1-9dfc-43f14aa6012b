import express from "express"
import { superAdminLogin, admin<PERSON>ogin, employeeRegister, employeeLogin, verifyEmployeeEmail } from "../controllers/authController.js"

const router = express.Router()

router.post("/superadmin/login", superAdminLogin)
router.post("/admin/login", adminLogin)
router.post("/employee/register", employeeRegister)
router.post("/employee/verify-email", verifyEmployeeEmail)
router.post("/employee/login", employeeLogin)

export default router
