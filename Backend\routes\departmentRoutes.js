import express from "express"
import {
  createDepartment,
  getAllDepartments,
  updateDepartment,
  toggleDepartmentStatus,
} from "../controllers/departmentController.js"
import { verifyToken, superAdminReadWrite, anySuperAdmin } from "../middleware/auth.js"

const router = express.Router()

router.post("/create", verifyToken, superAdminReadWrite, createDepartment)
router.get("/all", getAllDepartments)
router.put("/:departmentId", verifyToken, superAdminReadWrite, updateDepartment)
router.put("/:departmentId/toggle-status", verifyToken, superAdminReadWrite, toggleDepartmentStatus)

export default router
