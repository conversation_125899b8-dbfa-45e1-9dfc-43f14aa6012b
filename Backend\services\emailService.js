import nodemailer from "nodemailer"
import crypto from "crypto"

// Create transporter for sending emails
const createTransporter = () => {
  return nodemailer.createTransporter({
    service: process.env.EMAIL_SERVICE || "gmail",
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD,
    },
  })
}

// Generate a 6-digit OTP
export const generateOTP = () => {
  return crypto.randomInt(100000, 999999).toString()
}

// Generate verification token
export const generateVerificationToken = () => {
  return crypto.randomBytes(32).toString("hex")
}

// Send OTP email for employee registration
export const sendOTPEmail = async (email, otp, name) => {
  try {
    const transporter = createTransporter()

    const mailOptions = {
      from: {
        name: "TaskManager System",
        address: process.env.EMAIL_USER,
      },
      to: email,
      subject: "Email Verification - TaskManager Registration",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #2563eb; margin: 0;">TaskManager</h1>
              <p style="color: #6b7280; margin: 5px 0;">Time Tracking System</p>
            </div>
            
            <h2 style="color: #1f2937; margin-bottom: 20px;">Welcome ${name}!</h2>
            
            <p style="color: #4b5563; line-height: 1.6; margin-bottom: 25px;">
              Thank you for registering with TaskManager. To complete your registration and verify your email address, 
              please use the following One-Time Password (OTP):
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <div style="background-color: #f3f4f6; border: 2px dashed #d1d5db; border-radius: 8px; padding: 20px; display: inline-block;">
                <span style="font-size: 32px; font-weight: bold; color: #2563eb; letter-spacing: 5px;">${otp}</span>
              </div>
            </div>
            
            <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 25px 0; border-radius: 4px;">
              <p style="color: #92400e; margin: 0; font-weight: 500;">
                ⚠️ Important: This OTP will expire in 10 minutes for security reasons.
              </p>
            </div>
            
            <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
              If you didn't request this registration, please ignore this email. Your account will not be created without verification.
            </p>
            
            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 30px; text-align: center;">
              <p style="color: #9ca3af; font-size: 14px; margin: 0;">
                This is an automated message from TaskManager System. Please do not reply to this email.
              </p>
            </div>
          </div>
        </div>
      `,
      text: `
        Welcome to TaskManager, ${name}!
        
        Your email verification OTP is: ${otp}
        
        This OTP will expire in 10 minutes.
        
        If you didn't request this registration, please ignore this email.
        
        TaskManager System
      `,
    }

    const result = await transporter.sendMail(mailOptions)
    console.log("OTP email sent successfully:", result.messageId)
    return { success: true, messageId: result.messageId }
  } catch (error) {
    console.error("Error sending OTP email:", error)
    return { success: false, error: error.message }
  }
}

// Send welcome email after successful verification
export const sendWelcomeEmail = async (email, name, username) => {
  try {
    const transporter = createTransporter()

    const mailOptions = {
      from: {
        name: "TaskManager System",
        address: process.env.EMAIL_USER,
      },
      to: email,
      subject: "Welcome to TaskManager - Account Activated",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #2563eb; margin: 0;">TaskManager</h1>
              <p style="color: #6b7280; margin: 5px 0;">Time Tracking System</p>
            </div>
            
            <div style="text-align: center; margin-bottom: 30px;">
              <div style="background-color: #10b981; color: white; padding: 15px; border-radius: 50px; display: inline-block;">
                <span style="font-size: 24px;">✓</span>
              </div>
            </div>
            
            <h2 style="color: #1f2937; text-align: center; margin-bottom: 20px;">Account Successfully Activated!</h2>
            
            <p style="color: #4b5563; line-height: 1.6; margin-bottom: 25px;">
              Congratulations ${name}! Your TaskManager account has been successfully verified and activated.
            </p>
            
            <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 25px 0;">
              <h3 style="color: #1f2937; margin-top: 0;">Your Account Details:</h3>
              <p style="color: #4b5563; margin: 5px 0;"><strong>Name:</strong> ${name}</p>
              <p style="color: #4b5563; margin: 5px 0;"><strong>Username:</strong> ${username}</p>
              <p style="color: #4b5563; margin: 5px 0;"><strong>Email:</strong> ${email}</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/employee/login" 
                 style="background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block;">
                Login to Your Account
              </a>
            </div>
            
            <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
              You can now log in to your account and start tracking your tasks and time. If you have any questions or need assistance, 
              please contact your system administrator.
            </p>
            
            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 30px; text-align: center;">
              <p style="color: #9ca3af; font-size: 14px; margin: 0;">
                Welcome to the team! - TaskManager System
              </p>
            </div>
          </div>
        </div>
      `,
      text: `
        Welcome to TaskManager, ${name}!
        
        Your account has been successfully verified and activated.
        
        Account Details:
        - Name: ${name}
        - Username: ${username}
        - Email: ${email}
        
        You can now log in to your account at: ${process.env.FRONTEND_URL || 'http://localhost:5173'}/employee/login
        
        Welcome to the team!
        TaskManager System
      `,
    }

    const result = await transporter.sendMail(mailOptions)
    console.log("Welcome email sent successfully:", result.messageId)
    return { success: true, messageId: result.messageId }
  } catch (error) {
    console.error("Error sending welcome email:", error)
    return { success: false, error: error.message }
  }
}
